# Dynamic Configuration System Implementation

## Overview

This document summarizes the comprehensive implementation of a dynamic configuration system that replaces hardcoded values throughout the codebase with adaptive, system-aware parameters.

## 🎯 Objectives Achieved

### 1. **Eliminated Hardcoded Values**
- ✅ Replaced static mutation rates, population sizes, and generation limits
- ✅ Made AI model dimensions adaptive to system resources
- ✅ Dynamic visualization parameters based on sequence complexity
- ✅ Adaptive performance limits based on available hardware
- ✅ Configurable interaction cutoffs and thresholds

### 2. **Implemented Dynamic Configuration System**
- ✅ Centralized configuration manager (`DynamicConfigurationManager`)
- ✅ System resource detection (CPU, memory, GPU)
- ✅ Sequence complexity analysis
- ✅ Adaptive parameter calculation
- ✅ Runtime optimization based on current load

### 3. **Added Memory Usage Monitoring**
- ✅ Real-time memory tracking (`MemoryMonitor`)
- ✅ GPU memory monitoring
- ✅ Memory trend analysis
- ✅ Automatic optimization recommendations
- ✅ Alert system for memory issues

## 📁 New Files Created

### Core Configuration System
1. **`backend/utils/dynamic_config.py`**
   - Main configuration manager
   - System resource detection
   - Sequence complexity analysis
   - Dynamic parameter generation

2. **`backend/utils/constants.py`**
   - Dynamic constants system
   - Configurable amino acid properties
   - Adaptive thresholds and cutoffs
   - Performance limits calculation

3. **`backend/utils/memory_monitor.py`**
   - Real-time memory monitoring
   - Memory usage alerts
   - Optimization recommendations
   - GPU memory tracking

### Configuration and Testing
4. **`config/dynamic_config_template.json`**
   - Configuration template
   - User-customizable parameters
   - Default values and ranges

5. **`test_dynamic_configuration.py`**
   - Comprehensive test suite
   - Integration testing
   - Performance validation

## 🔧 Modified Files

### Main Framework
- **`main.py`**: Updated to use dynamic configuration system
- **`frontend/streamlit_app.py`**: Integrated with centralized config manager

### Core Components
- **`backend/simulator/mutation_engine.py`**: Dynamic amino acids and performance limits
- **`backend/analyzer/structural_biology.py`**: Dynamic API URLs and thresholds
- **`backend/analyzer/protein_interaction.py`**: Dynamic interaction cutoffs

## 🚀 Key Features

### 1. **System-Aware Configuration**
```python
# Automatically detects system resources
config_manager = get_dynamic_config_manager()
print(f"CPUs: {config_manager.system_resources.cpu_count}")
print(f"Memory: {config_manager.system_resources.memory_gb} GB")
print(f"GPU: {config_manager.system_resources.gpu_available}")
```

### 2. **Sequence Complexity Analysis**
```python
# Calculates complexity metrics
complexity = config_manager.calculate_sequence_complexity(sequence)
print(f"Complexity Score: {complexity.complexity_score}")
print(f"Entropy: {complexity.entropy}")
```

### 3. **Adaptive Parameters**
```python
# Parameters adapt to sequence and system
sim_params = config_manager.get_simulation_parameters(sequence)
ai_params = config_manager.get_ai_model_parameters(sequence)
viz_params = config_manager.get_visualization_parameters(sequence)
```

### 4. **Memory Monitoring**
```python
# Real-time memory tracking
monitor = get_memory_monitor()
usage = monitor.get_current_usage()
recommendations = monitor.get_optimization_recommendations()
```

## 📊 Dynamic Parameter Examples

### Simulation Parameters
| Sequence Length | Mutation Rate | Max Generations | Population Size |
|----------------|---------------|-----------------|-----------------|
| 50 AA          | 0.0008        | 15              | 8,000          |
| 500 AA         | 0.0015        | 35              | 25,000         |
| 2000 AA        | 0.002         | 60              | 50,000         |

### AI Model Parameters
| System Memory | GNN Hidden Dim | Transformer D-Model | Batch Size |
|---------------|----------------|---------------------|------------|
| 8 GB          | 128            | 256                 | 32         |
| 16 GB         | 256            | 512                 | 64         |
| 32 GB         | 512            | 768                 | 128        |

### Visualization Parameters
| Sequence Length | Width x Height | Max Residues | Animation FPS |
|----------------|----------------|--------------|---------------|
| 100 AA         | 900 x 500      | 100          | 30            |
| 500 AA         | 1200 x 700     | 500          | 30            |
| 1000 AA        | 1400 x 900     | 1000         | 60            |

## 🎛️ Configuration Options

### User Customization
Users can override any parameter:
```python
user_config = {
    'mutation_rate': 0.005,
    'max_generations': 50,
    'gnn_hidden_dim': 256
}
framework = VirusMutationSimulationFramework(config=user_config, sequence=sequence)
```

### Environment Variables
```bash
# GPU settings
CUDA_VISIBLE_DEVICES=0
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128

# API URLs
ALPHAFOLD_API_URL=https://alphafold.ebi.ac.uk/api/prediction/
UNIPROT_API_URL=https://rest.uniprot.org/
```

## 🔍 Memory Optimization Features

### Automatic Monitoring
- Real-time system and GPU memory tracking
- Memory usage trend analysis
- Automatic alerts for high usage

### Optimization Recommendations
- Reduce batch sizes when memory is high
- Enable memory-efficient mode
- Clear caches automatically
- Adjust worker counts

### Performance Adaptation
- Lower quality settings for limited resources
- Adaptive chunk sizes for processing
- Dynamic timeout values
- Fallback mechanisms

## 🧪 Testing and Validation

### Test Coverage
- ✅ System resource detection
- ✅ Sequence complexity calculation
- ✅ Dynamic parameter generation
- ✅ Memory monitoring functionality
- ✅ Integration with existing components
- ✅ Performance adaptation

### Running Tests
```bash
python test_dynamic_configuration.py
```

## 📈 Performance Benefits

### Before (Hardcoded)
- Fixed parameters regardless of system capabilities
- No memory monitoring
- Static visualization settings
- Manual parameter tuning required

### After (Dynamic)
- Parameters adapt to available resources
- Real-time memory monitoring and optimization
- Automatic quality adjustment
- Self-tuning based on sequence complexity

## 🔮 Future Enhancements

### Planned Features
1. **Machine Learning-Based Optimization**
   - Learn optimal parameters from usage patterns
   - Predictive resource allocation

2. **Cloud Integration**
   - Auto-scaling for cloud deployments
   - Resource cost optimization

3. **Advanced Profiling**
   - Detailed performance metrics
   - Bottleneck identification

4. **User Preferences Learning**
   - Remember user customizations
   - Suggest optimal settings

## 🛠️ Usage Examples

### Basic Usage
```python
# Initialize with dynamic configuration
framework = VirusMutationSimulationFramework(sequence="ACDEFGHIKLMNPQRSTVWY")

# Parameters are automatically optimized
print(f"Mutation rate: {framework.config['mutation_rate']}")
print(f"Population size: {framework.config['population_size']}")
```

### Advanced Customization
```python
# Get configuration manager
config_manager = get_dynamic_config_manager()

# Customize for specific needs
custom_params = config_manager.get_simulation_parameters(
    sequence, 
    user_preferences={'mutation_rate': 0.01, 'max_generations': 100}
)
```

### Memory Monitoring
```python
# Start monitoring
start_memory_monitoring(interval=1.0)

# Get current status
usage = get_current_memory_usage()
recommendations = get_memory_optimization_recommendations()
```

## 📝 Configuration File

Users can create custom configuration files based on the template:
```json
{
  "simulation": {
    "base_mutation_rate": 0.001,
    "complexity_scaling": true,
    "resource_scaling": true
  },
  "ai_models": {
    "auto_scale_dimensions": true,
    "memory_efficient_mode": false
  },
  "performance": {
    "auto_detect_workers": true,
    "memory_monitoring": true
  }
}
```

## 🎉 Conclusion

The dynamic configuration system successfully eliminates hardcoded values and provides:

1. **Adaptive Performance**: Parameters automatically adjust to system capabilities
2. **Memory Efficiency**: Real-time monitoring and optimization
3. **User Flexibility**: Easy customization while maintaining intelligent defaults
4. **Future-Proof**: Extensible architecture for new features

The system is now fully dynamic, self-optimizing, and ready for production use across different hardware configurations and use cases.
