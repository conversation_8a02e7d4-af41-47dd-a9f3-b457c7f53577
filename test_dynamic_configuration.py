#!/usr/bin/env python3
"""
Test Dynamic Configuration System
Comprehensive testing of the dynamic configuration and constants system
"""

import os
import sys
import time
import logging
from typing import Dict, Any

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dynamic_config_manager():
    """Test the dynamic configuration manager"""
    print("🧪 Testing Dynamic Configuration Manager...")
    
    try:
        from backend.utils.dynamic_config import get_dynamic_config_manager
        
        # Initialize configuration manager
        config_manager = get_dynamic_config_manager()
        
        # Test system resource detection
        print(f"✅ System Resources Detected:")
        print(f"   CPU Cores: {config_manager.system_resources.cpu_count}")
        print(f"   Memory: {config_manager.system_resources.memory_gb:.1f} GB")
        print(f"   GPU Available: {config_manager.system_resources.gpu_available}")
        if config_manager.system_resources.gpu_available:
            print(f"   GPU Memory: {config_manager.system_resources.gpu_memory_gb:.1f} GB")
        
        # Test sequence complexity calculation
        test_sequences = [
            "ACDEFGHIKLMNPQRSTVWY",  # Short sequence
            "ACDEFGHIKLMNPQRSTVWY" * 10,  # Medium sequence
            "ACDEFGHIKLMNPQRSTVWY" * 50   # Long sequence
        ]
        
        for i, sequence in enumerate(test_sequences):
            complexity = config_manager.calculate_sequence_complexity(sequence)
            print(f"✅ Sequence {i+1} (length {len(sequence)}):")
            print(f"   Complexity Score: {complexity.complexity_score:.3f}")
            print(f"   Entropy: {complexity.entropy:.3f}")
            print(f"   GC Content: {complexity.gc_content:.3f}")
        
        # Test dynamic parameter generation
        for i, sequence in enumerate(test_sequences):
            sim_params = config_manager.get_simulation_parameters(sequence)
            ai_params = config_manager.get_ai_model_parameters(sequence)
            viz_params = config_manager.get_visualization_parameters(sequence)
            perf_params = config_manager.get_performance_parameters(sequence)
            
            print(f"✅ Dynamic Parameters for Sequence {i+1}:")
            print(f"   Mutation Rate: {sim_params.mutation_rate:.4f}")
            print(f"   Max Generations: {sim_params.max_generations}")
            print(f"   Population Size: {sim_params.population_size}")
            print(f"   GNN Hidden Dim: {ai_params.gnn_hidden_dim}")
            print(f"   Visualization Size: {viz_params.default_width}x{viz_params.default_height}")
            print(f"   Max Workers: {perf_params.max_workers}")
        
        print("✅ Dynamic Configuration Manager tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Dynamic Configuration Manager test failed: {e}")
        return False

def test_dynamic_constants():
    """Test the dynamic constants system"""
    print("\n🧪 Testing Dynamic Constants...")
    
    try:
        from backend.utils.constants import (get_dynamic_constants, get_amino_acids, 
                                           get_amino_acid_properties, get_interaction_cutoffs,
                                           get_visualization_defaults, get_performance_limits)
        
        # Test constants access
        constants = get_dynamic_constants()
        
        # Test amino acids
        amino_acids = get_amino_acids()
        print(f"✅ Amino Acids: {len(amino_acids)} acids loaded")
        
        # Test amino acid properties
        aa_properties = get_amino_acid_properties()
        print(f"✅ Amino Acid Properties: {len(aa_properties)} properties loaded")
        
        # Test interaction cutoffs
        cutoffs_standard = get_interaction_cutoffs("standard")
        cutoffs_high = get_interaction_cutoffs("high")
        cutoffs_low = get_interaction_cutoffs("low")
        
        print(f"✅ Interaction Cutoffs:")
        print(f"   Standard: {cutoffs_standard}")
        print(f"   High Precision: {cutoffs_high}")
        print(f"   Low Precision: {cutoffs_low}")
        
        # Test visualization defaults for different sequence lengths
        for length in [50, 200, 1000]:
            viz_defaults = get_visualization_defaults(length)
            print(f"✅ Visualization Defaults (length {length}): {viz_defaults}")
        
        # Test performance limits
        for length in [50, 200, 1000]:
            perf_limits = get_performance_limits(length)
            print(f"✅ Performance Limits (length {length}): {perf_limits}")
        
        print("✅ Dynamic Constants tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Dynamic Constants test failed: {e}")
        return False

def test_memory_monitor():
    """Test the memory monitoring system"""
    print("\n🧪 Testing Memory Monitor...")
    
    try:
        from backend.utils.memory_monitor import (get_memory_monitor, start_memory_monitoring,
                                                get_current_memory_usage, get_memory_optimization_recommendations)
        
        # Get memory monitor
        monitor = get_memory_monitor()
        
        # Test current usage
        usage = get_current_memory_usage()
        print(f"✅ Current Memory Usage:")
        print(f"   System Memory: {usage['system_memory_percent']:.1f}%")
        print(f"   Available Memory: {usage['available_memory_gb']:.1f} GB")
        print(f"   Process Memory: {usage['process_memory_gb']:.1f} GB")
        if usage['gpu_memory_percent'] > 0:
            print(f"   GPU Memory: {usage['gpu_memory_percent']:.1f}%")
        
        # Test recommendations
        recommendations = get_memory_optimization_recommendations()
        if recommendations:
            print(f"✅ Memory Optimization Recommendations:")
            for rec in recommendations[:3]:  # Show first 3
                print(f"   - {rec}")
        else:
            print("✅ No memory optimization needed")
        
        # Test monitoring (brief)
        print("✅ Starting brief memory monitoring test...")
        start_memory_monitoring(interval=0.5)
        time.sleep(2)  # Monitor for 2 seconds
        
        # Get trend
        trend = monitor.get_memory_trend(minutes=1)
        print(f"✅ Memory Trend: {trend['trend']} (change rate: {trend['change_rate']:.3f}%/s)")
        
        monitor.stop_monitoring()
        
        print("✅ Memory Monitor tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Memory Monitor test failed: {e}")
        return False

def test_integration():
    """Test integration with existing components"""
    print("\n🧪 Testing Integration with Existing Components...")
    
    try:
        # Test with main framework
        from main import VirusMutationSimulationFramework
        
        test_sequence = "ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY"
        
        # Initialize framework with dynamic configuration
        framework = VirusMutationSimulationFramework(sequence=test_sequence)
        
        print(f"✅ Framework initialized with dynamic configuration")
        print(f"   Mutation Rate: {framework.config['mutation_rate']:.4f}")
        print(f"   Max Generations: {framework.config['max_generations']}")
        print(f"   Population Size: {framework.config['population_size']}")
        print(f"   GNN Hidden Dim: {framework.config['gnn_hidden_dim']}")
        print(f"   Max Workers: {framework.config['max_workers']}")
        
        # Test mutation engine with dynamic config
        from backend.simulator.mutation_engine import MutationEngine
        
        mutation_engine = MutationEngine(test_sequence)
        print(f"✅ Mutation Engine initialized with dynamic amino acids: {len(mutation_engine.amino_acids)} acids")
        
        # Test structural analyzer with dynamic config
        from backend.analyzer.structural_biology import StructuralBiologyAnalyzer
        
        structural_analyzer = StructuralBiologyAnalyzer()
        print(f"✅ Structural Analyzer initialized with dynamic API URLs")
        
        print("✅ Integration tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def test_performance_adaptation():
    """Test performance adaptation based on system load"""
    print("\n🧪 Testing Performance Adaptation...")
    
    try:
        from backend.utils.dynamic_config import get_dynamic_config_manager
        
        config_manager = get_dynamic_config_manager()
        
        # Test different sequence lengths
        sequences = [
            "A" * 50,    # Short
            "A" * 500,   # Medium  
            "A" * 2000   # Long
        ]
        
        for i, sequence in enumerate(sequences):
            print(f"✅ Sequence Length {len(sequence)}:")
            
            # Get performance parameters
            perf_params = config_manager.get_performance_parameters(sequence)
            ai_params = config_manager.get_ai_model_parameters(sequence)
            
            print(f"   Max Workers: {perf_params.max_workers}")
            print(f"   Chunk Size: {perf_params.chunk_size}")
            print(f"   Memory Limit: {perf_params.memory_limit_gb:.1f} GB")
            print(f"   AI Hidden Dim: {ai_params.gnn_hidden_dim}")
            print(f"   Batch Size: {ai_params.batch_size}")
        
        # Test optimization for current load
        optimization = config_manager.optimize_for_current_load("A" * 1000)
        print(f"✅ Current Load Optimization: {optimization}")
        
        print("✅ Performance Adaptation tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Performance Adaptation test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Dynamic Configuration System Tests\n")
    
    tests = [
        test_dynamic_config_manager,
        test_dynamic_constants,
        test_memory_monitor,
        test_integration,
        test_performance_adaptation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()  # Add spacing between tests
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Dynamic configuration system is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
