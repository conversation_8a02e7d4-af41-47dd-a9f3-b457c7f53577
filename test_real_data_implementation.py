#!/usr/bin/env python3
"""
Test Real Data Implementation - Verify all real analytics functions are working
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import numpy as np
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Dict, List, Tuple

# Mock MutationNode for testing
@dataclass
class MockMutationNode:
    id: str
    sequence: str
    parent_id: str
    mutations: List[Tuple]
    fitness: float
    generation: int
    timestamp: datetime

def create_test_mutation_tree(reference_sequence: str, num_nodes: int = 50) -> Dict:
    """Create a test mutation tree for testing real analytics"""
    
    mutation_tree = {}
    amino_acids = ['A', 'R', 'N', 'D', 'C', 'Q', 'E', 'G', 'H', 'I',
                   'L', 'K', 'M', 'F', 'P', 'S', 'T', 'W', 'Y', 'V']
    
    # Create root node
    root = MockMutationNode(
        id="root",
        sequence=reference_sequence,
        parent_id=None,
        mutations=[],
        fitness=1.0,
        generation=0,
        timestamp=datetime.now() - timedelta(minutes=60)
    )
    mutation_tree["root"] = root
    
    # Create child nodes with realistic mutations
    for i in range(1, num_nodes):
        # Generate mutations
        num_mutations = np.random.randint(1, 4)
        mutations = []
        
        for _ in range(num_mutations):
            pos = np.random.randint(0, len(reference_sequence))
            from_aa = reference_sequence[pos] if pos < len(reference_sequence) else 'A'
            to_aa = np.random.choice(amino_acids)
            mutations.append((pos, from_aa, to_aa))
        
        # Calculate fitness (decreases with more mutations)
        fitness = max(0.1, 1.0 - (len(mutations) * 0.1) + np.random.normal(0, 0.05))
        
        # Assign generation
        generation = min(10, i // 5)
        
        # Create timestamp
        timestamp = datetime.now() - timedelta(minutes=60 - i)
        
        node = MockMutationNode(
            id=f"node_{i}",
            sequence=reference_sequence,  # Simplified - would be mutated sequence
            parent_id="root" if i <= 5 else f"node_{np.random.randint(1, i)}",
            mutations=mutations,
            fitness=fitness,
            generation=generation,
            timestamp=timestamp
        )
        
        mutation_tree[f"node_{i}"] = node
    
    return mutation_tree

def test_real_mutation_analytics():
    """Test real mutation analytics functions"""
    print("\n🧬 Testing Real Mutation Analytics")
    print("=" * 50)
    
    try:
        from backend.analyzer.real_mutation_analytics import get_real_mutation_analytics
        
        # Create test data
        reference_sequence = "MFVFLVLLPLVSSQCVNLTTRTQLPPAYTNSFTRGVYYPDKVFRSSVLHSTQDLFLPFFSNVTWFHAIHVSGTNGTKRFDNPVLPFNDGVYFASTEKSNIIRGWIFGTTLDSKTQSLLIVNNATNVVIKVCEFQFCNDPFLGVYYHKNNKSWMESEFRVYSSANNCTFEYVSQPFLMDLEGKQGNFKNLREFVFKNIDGYFKIYSKHTPINLVRDLPQGFSALEPLVDLPIGINITRFQTLLALHRSYLTPGDSSSGWTAGAAAYYVGYLQPRTFLLKYNENGTITDAVDCALDPLSETKCTLKSFTVEKGIYQTSNFRVQPTESIVRFPNITNLCPFGEVFNATRFASVYAWNRKRISNCVADYSVLYNSASFSTFKCYGVSPTKLNDLCFTNVYADSFVIRGDEVRQIAPGQTGKIADYNYKLPDDFTGCVIAWNSNNLDSKVGGNYNYLYRLFRKSNLKPFERDISTEIYQAGSTPCNGVEGFNCYFPLQSYGFQPTNGVGYQPYRVVVLSFELLHAPATVCGPKKSTNLVKNKCVNFNFNGLTGTGVLTESNKKFLPFQQFGRDIADTTDAVRDPQTLEILDITPCSFGGVSVITPGTNTSNQVAVLYQDVNCTEVPVAIHADQLTPTWRVYSTGSNVFQTRAGCLIGAEHVNNSYECDIPIGAGICASYQTQTNSPRRARSVASQSIIAYTMSLGAENSVAYSNNSIAIPTNFTISVTTEILPVSMTKTSVDCTMYICGDSTECSNLLLQYGSFCTQLNRALTGIAVEQDKNTQEVFAQVKQIYKTPPIKDFGGFNFSQILPDPSKPSKRSFIEDLLFNKVTLADAGFIKQYGDCLGDIAARDLICAQKFNGLTVLPPLLTDEMIAQYTSALLAGTITSGWTFGAGAALQIPFAMQMAYRFNGIGVTQNVLYENQKLIANQFNSAIGKIQDSLSSTASALGKLQDVVNQNAQALNTLVKQLSSNFGAISSVLNDILSRLDKVEAEVQIDRLITGRLQSLQTYVTQQLIRAAEIRASANLAATKMSECVLGQSKRVDFCGKGYHLMSFPQSAPHGVVFLHVTYVPAQEKNFTTAPAICHDGKAHFPREGVFVSNGTHWFVTQRNFYEPQIITTDNTFVSGNCDVVIGIVNNTVYDPLQPELDSFKEELDKYFKNHTSPDVDLGDISGINASVVNIQKEIDRLNEVAKNLNESLIDLQELGKYEQYIKWPWYIWLGFIAGLIAIVMVTIMLCCMTSCCSCLKGCCSCGSCCKFDEDDSEPVLKGVKLHYT"
        
        mutation_tree = create_test_mutation_tree(reference_sequence, 30)
        
        analytics = get_real_mutation_analytics()
        
        # Test mutation frequency analysis
        print("📊 Testing mutation frequency analysis...")
        freq_data = analytics.analyze_mutation_frequencies(mutation_tree, reference_sequence)
        print(f"   ✅ Total mutations: {freq_data.total_mutations}")
        print(f"   ✅ Hotspot positions: {len(freq_data.hotspot_positions)}")
        print(f"   ✅ Frequency matrix shape: {freq_data.frequency_matrix.shape}")
        
        # Test temporal evolution
        print("⏱️ Testing temporal evolution analysis...")
        temporal_data = analytics.track_temporal_evolution(mutation_tree)
        print(f"   ✅ Time points: {len(temporal_data.time_points)}")
        print(f"   ✅ Max mutations: {max(temporal_data.mutation_counts) if temporal_data.mutation_counts else 0}")
        print(f"   ✅ Final diversity: {temporal_data.diversity_scores[-1]:.3f}" if temporal_data.diversity_scores else "   ✅ Final diversity: 0.000")
        
        # Test conservation analysis
        print("🧬 Testing conservation analysis...")
        conservation_data = analytics.analyze_conservation(mutation_tree, reference_sequence)
        print(f"   ✅ Overall conservation: {conservation_data.conservation_scores['overall_sequence_conservation']:.3f}")
        print(f"   ✅ Functional domains: {len(conservation_data.functional_domains)}")
        print(f"   ✅ Highly conserved positions: {conservation_data.conservation_scores['highly_conserved_positions']}")
        
        print("✅ Real Mutation Analytics: ALL TESTS PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Real Mutation Analytics test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_performance_analysis():
    """Test real performance analysis functions"""
    print("\n🤖 Testing Real Performance Analysis")
    print("=" * 50)
    
    try:
        from backend.analyzer.real_performance_analysis import get_real_performance_analyzer
        
        # Create test data
        test_sequences = [
            "MFVFLVLLPLVSSQCVNLTTRTQLPPAYTNSFTRGVYYPDKVFRSSVLH",
            "ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY",
            "GPKKSTNLVKNKCVNFNFNGLTGTGVLTESNKKFLPFQQFGRDIADTTDAVRDPQTLEILDITPCSFGGVSVITPGTNTSNQVAVLYQDVNCTEVPVAIHADQLTPTWRVYSTGSNVFQTRAGCLIGAEHVNNSYECDIPIGAGICASYQTQTNSPRRARSVASQSIIAYTMSLGAENSVAYSNNSIAIPTNFTISVTTEILPVSMTKTSVDCTMYICGDSTECSNLLLQYGSFCTQLNRALTGIAVEQDKNTQEVFAQVKQIYKTPPIKDFGGFNFSQILPDPSKPSKRSFIEDLLFNKVTLADAGFIKQYGDCLGDIAARDLICAQKFNGLTVLPPLLTDEMIAQYTSALLAGTITSGWTFGAGAALQIPFAMQMAYRFNGIGVTQNVLYENQKLIANQFNSAIGKIQDSLSSTASALGKLQDVVNQNAQALNTLVKQLSSNFGAISSVLNDILSRLDKVEAEVQIDRLITGRLQSLQTYVTQQLIRAAEIRASANLAATKMSECVLGQSKRVDFCGKGYHLMSFPQSAPHGVVFLHVTYVPAQEKNFTTAPAICHDGKAHFPREGVFVSNGTHWFVTQRNFYEPQIITTDNTFVSGNCDVVIGIVNNTVYDPLQPELDSFKEELDKYFKNHTSPDVDLGDISGINASVVNIQKEIDRLNEVAKNLNESLIDLQELGKYEQYIKWPWYIWLGFIAGLIAIVMVTIMLCCMTSCCSCLKGCCSCGSCCKFDEDDSEPVLKGVKLHYT"
        ]
        
        analyzer = get_real_performance_analyzer()
        
        # Generate test data
        print("📊 Generating test data...")
        test_data = analyzer.generate_test_data(test_sequences, num_samples=20)
        print(f"   ✅ Generated {len(test_data)} test samples")
        
        # Test cross-validation
        print("🔄 Testing cross-validation...")
        cv_results = analyzer.perform_cross_validation(test_data, k_folds=3)
        print(f"   ✅ Mean accuracy: {cv_results.mean_accuracy:.3f} ± {cv_results.std_accuracy:.3f}")
        print(f"   ✅ Mean F1: {cv_results.mean_f1:.3f} ± {cv_results.std_f1:.3f}")
        
        # Test benchmarking
        print("🏆 Testing benchmarking...")
        benchmark_results = analyzer.benchmark_against_baselines(test_data)
        print(f"   ✅ Methods compared: {len(benchmark_results.method_names)}")
        print(f"   ✅ Best method: {benchmark_results.ranking[0]}")
        
        print("✅ Real Performance Analysis: ALL TESTS PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Real Performance Analysis test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_data_integration():
    """Test real data integration functions"""
    print("\n🔗 Testing Real Data Integration")
    print("=" * 50)
    
    try:
        from backend.analyzer.real_data_integration import get_real_data_integrator
        
        # Create test data
        reference_sequence = "MFVFLVLLPLVSSQCVNLTTRTQLPPAYTNSFTRGVYYPDKVFRSSVLH"
        mutation_tree = create_test_mutation_tree(reference_sequence, 20)
        test_sequences = [reference_sequence]
        
        integrator = get_real_data_integrator()
        
        # Test mutation frequency data integration
        print("📊 Testing mutation frequency data integration...")
        freq_data = integrator.get_real_mutation_frequency_data(mutation_tree, reference_sequence)
        print(f"   ✅ Total mutations: {freq_data['total_mutations']}")
        print(f"   ✅ Analysis type: {freq_data['metadata']['analysis_type']}")
        
        # Test temporal evolution data integration
        print("⏱️ Testing temporal evolution data integration...")
        temporal_data = integrator.get_real_temporal_evolution_data(mutation_tree)
        print(f"   ✅ Time points: {len(temporal_data['time_points'])}")
        print(f"   ✅ Analysis type: {temporal_data['metadata']['analysis_type']}")
        
        # Test conservation analysis data integration
        print("🧬 Testing conservation analysis data integration...")
        conservation_data = integrator.get_real_conservation_analysis_data(mutation_tree, reference_sequence)
        print(f"   ✅ Functional domains: {conservation_data['metadata']['num_domains']}")
        print(f"   ✅ Analysis type: {conservation_data['metadata']['analysis_type']}")
        
        # Test performance comparison data integration
        print("🏆 Testing performance comparison data integration...")
        performance_data = integrator.get_real_performance_comparison_data(test_sequences)
        print(f"   ✅ Methods compared: {len(performance_data['methods'])}")
        print(f"   ✅ Best method: {performance_data['ranking'][0]}")
        print(f"   ✅ Analysis type: {performance_data['metadata']['analysis_type']}")
        
        # Test cross-validation data integration
        print("🔄 Testing cross-validation data integration...")
        cv_data = integrator.get_real_cross_validation_data(test_sequences, k_folds=3)
        print(f"   ✅ K-folds: {cv_data['metadata']['k_folds']}")
        print(f"   ✅ Mean accuracy: {cv_data['summary_statistics']['mean_accuracy']:.3f}")
        print(f"   ✅ Analysis type: {cv_data['metadata']['analysis_type']}")
        
        # Test AI insights data integration
        print("🤖 Testing AI insights data integration...")
        mutations = [(10, 'A', 'R'), (25, 'F', 'L')]
        ai_data = integrator.get_real_ai_insights_data(reference_sequence, mutations)
        print(f"   ✅ Predictions available: {'predictions' in ai_data}")
        print(f"   ✅ Feature importance: {len(ai_data['feature_importance']['mutation_positions'])}")
        print(f"   ✅ Analysis type: {ai_data['metadata']['analysis_type']}")
        
        print("✅ Real Data Integration: ALL TESTS PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Real Data Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all real data implementation tests"""
    print("🧪 Testing Real Data Implementation")
    print("=" * 60)
    print("This test verifies that all mock data has been replaced with real analytics")
    
    results = []
    
    # Test individual components
    results.append(test_real_mutation_analytics())
    results.append(test_real_performance_analysis())
    results.append(test_real_data_integration())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Tests Passed: {passed}/{total}")
    print(f"❌ Tests Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 ALL REAL DATA IMPLEMENTATIONS WORKING!")
        print("🚀 Mock data has been successfully replaced with real analytics")
        print("📊 The framework now provides genuine computed results")
    else:
        print("\n⚠️  Some tests failed - check the error messages above")
        print("🔧 Fix the failing components before using the real data system")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
